import { cache } from 'react';
import { notFound } from 'next/navigation';
import mongoose from 'mongoose';

import { can } from '@/lib/capabilities';
import { getAutomationModel, AutomationDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (automation: AutomationDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not view automations.');

  const sameAccount = account._id.equals(automation?.account) || isSystemAdmin;

  const canView = await can(user, 'view_automation', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view automations.');

  const canEdit = await can(user, 'edit_automation', sameAccount);
  const canDelete = await can(user, 'delete_automation', sameAccount);

  if (!automation?._id) notFound();

  return { canView, canEdit, canDelete, user, account, automation };
});
