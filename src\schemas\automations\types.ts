import type mongoose from 'mongoose';

import { Account } from '@/schemas/accounts';
import { User } from '@/schemas/users';

export enum AUTOMATION_TRIGGER_TYPE {
  PROJECT_CREATED = 'project_created',
  PROJECT_STATUS_CHANGED = 'project_status_changed',
  CUSTOMER_CREATED = 'customer_created',
  QUOTE_CREATED = 'quote_created',
  ORDER_CREATED = 'order_created',
}

export enum PROJECT_STATUS {
  LEAD = 'lead',
  PROSPECT = 'prospect',
  ESTIMATE = 'estimate',
  PROPOSAL = 'proposal',
  CONTRACT = 'contract',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELED = 'canceled',
}

export enum AUTOMATION_ACTION_TYPE {
  SEND_EMAIL = 'send_email',
  SEND_SMS = 'send_sms',
}

export interface ProjectStatusChangeTriggerConditions {
  fromStatus: PROJECT_STATUS;
  toStatus: PROJECT_STATUS;
  daysOfInactivity: number;
}

export interface AutomationTrigger {
  type: AUTOMATION_TRIGGER_TYPE;
  conditions?: Record<string, any>;
  projectStatusChangeConditions?: ProjectStatusChangeTriggerConditions;
}

export interface EmailRecipient {
  type: 'user' | 'custom';
  userId?: string;
  email?: string;
}

export interface SmsRecipient {
  type: 'user' | 'custom';
  userId?: string;
  phoneNumber?: string;
}

export interface EmailActionConfig {
  subject: string;
  body: string;
  recipients: EmailRecipient[];
}

export interface SmsActionConfig {
  message: string;
  recipients: SmsRecipient[];
}

export interface AutomationAction {
  type: AUTOMATION_ACTION_TYPE;
  config: Record<string, any>;
  emailConfig?: EmailActionConfig;
  smsConfig?: SmsActionConfig;
}

export interface Automation {
  _id: mongoose.Types.ObjectId;
  account: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  isActive: boolean;
  trigger: AutomationTrigger;
  actions: AutomationAction[];
  createdBy: mongoose.Types.ObjectId;
  created: Date;
  modifiedBy: mongoose.Types.ObjectId;
  modified: Date;
  documents?: {
    account?: Account;
    createdBy?: User;
    modifiedBy?: User;
  };
}

export type AutomationDoc = mongoose.Document<mongoose.Types.ObjectId, object, Automation> & Automation;

export type AutomationModel = mongoose.Model<Automation>;
