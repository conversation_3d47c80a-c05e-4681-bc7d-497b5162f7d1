import mongoose from 'mongoose';

import { getAccountModel } from '@/schemas/accounts';
import { getUserModel } from '@/schemas/users';
import { Automation, AutomationDoc } from './types';

export function AutomationMiddleware(schema: mongoose.Schema<Automation>) {
  schema.virtual('documents.account', {
    ref: 'Account',
    localField: 'account',
    foreignField: '_id',
    justOne: true,
  });

  schema.virtual('documents.createdBy', {
    ref: 'User',
    localField: 'createdBy',
    foreignField: '_id',
    justOne: true,
  });

  schema.virtual('documents.modifiedBy', {
    ref: 'User',
    localField: 'modifiedBy',
    foreignField: '_id',
    justOne: true,
  });

  schema.method('populate', async function(this: AutomationDoc) {
    try {
      if (!this.populated('documents.account') && this.account) {
        const accountModel = await getAccountModel();
        this.documents = {
          ...this.documents,
          account: await accountModel.findOne({ _id: this.account }),
        };
      }

      if (!this.populated('documents.createdBy') && this.createdBy) {
        const userModel = await getUserModel();
        this.documents = {
          ...this.documents,
          createdBy: await userModel.findOne({ _id: this.createdBy }),
        };
      }

      if (!this.populated('documents.modifiedBy') && this.modifiedBy) {
        const userModel = await getUserModel();
        this.documents = {
          ...this.documents,
          modifiedBy: await userModel.findOne({ _id: this.modifiedBy }),
        };
      }
    } catch (error) {
      console.error('Error in AutomationMiddleware.populate:', error);
    }
  });
}
