'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { getAutomationModel } from '@/schemas';
import { ActionResult } from '@/lib/form';

import { validatePermissions } from './permissions';

export async function toggleAutomationStatus(id: string, isActive: boolean): Promise<ActionResult> {
  try {
    const { account, canEdit } = await validatePermissions();

    if (!canEdit) {
      return { error: 'You do not have permission to edit automations' };
    }

    const automationModel = await getAutomationModel();
    const automation = await automationModel.findOne({ 
      _id: new mongoose.Types.ObjectId(id),
      account: account._id 
    });

    if (!automation) {
      return { error: 'Automation not found' };
    }

    automation.isActive = isActive;
    await automation.save();

    revalidatePath('/automation');
    
    return { 
      error: null, 
      message: `Automation ${isActive ? 'activated' : 'deactivated'} successfully` 
    };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }
}
