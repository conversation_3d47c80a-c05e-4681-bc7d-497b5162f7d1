'use client';

import { useState } from 'react';
import { Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AutomationToggleProps {
  defaultChecked?: boolean;
  name: string;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
}

export function AutomationToggle({
  defaultChecked = false,
  name,
  onChange,
  disabled = false,
}: AutomationToggleProps) {
  const [isActive, setIsActive] = useState(defaultChecked);

  const handleToggle = () => {
    if (disabled) return;

    const newState = !isActive;
    setIsActive(newState);

    if (onChange) {
      onChange(newState);
    }
  };

  return (
    <div className="flex items-center">
      <input
        type="checkbox"
        name={name}
        checked={isActive}
        onChange={() => {}}
        className="hidden"
        disabled={disabled}
      />

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              type="button"
              onClick={handleToggle}
              className={cn(
                "flex items-center justify-center w-10 h-10 rounded-full transition-colors",
                isActive
                  ? "bg-orange-500 text-white hover:bg-orange-600"
                  : "bg-gray-200 text-gray-500 hover:bg-gray-300",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={disabled}
              aria-checked={isActive}
              role="switch"
            >
              <Zap className={cn(
                "h-5 w-5",
                isActive ? "text-white" : "text-gray-500"
              )} />
            </button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Automation Status: {isActive ? 'Active' : 'Inactive'}</p>
            <p className="text-xs text-muted-foreground">
              {isActive
                ? 'This automation will run automatically'
                : 'This automation is currently disabled'}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
