import { cache } from 'react';
import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { getAutomationModel } from '@/schemas';
import { ActionResult } from '@/lib/form';

import { validatePermissions } from './permissions';

export const getData = cache(async function () {
  'use server';
  const permissions = await validatePermissions();
  const { account } = permissions;

  const automationModel = await getAutomationModel();
  const automations = await automationModel.find({ account: account._id }).sort({ name: 1 });

  return { ...permissions, automations };
});

export async function toggleAutomationStatus(id: string, isActive: boolean): Promise<ActionResult> {
  'use server';
  try {
    const { account, canEdit } = await validatePermissions();

    if (!canEdit) {
      return { error: 'You do not have permission to edit automations' };
    }

    const automationModel = await getAutomationModel();
    const automation = await automationModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      account: account._id
    });

    if (!automation) {
      return { error: 'Automation not found' };
    }

    automation.isActive = isActive;
    await automation.save();

    revalidatePath('/automation');

    return {
      error: null,
      message: `Automation ${isActive ? 'activated' : 'deactivated'} successfully`
    };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }
}
