import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';
import { validateRequest } from '@/server/auth';
import { can } from '@/lib/capabilities';

export async function POST(request: NextRequest) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const canEdit = await can(user, 'edit_automation');
    if (!canEdit) {
      return NextResponse.json({ error: 'You do not have permission to edit automations' }, { status: 403 });
    }

    const { automationId, isActive } = await request.json();

    if (!automationId || typeof isActive !== 'boolean') {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 });
    }

    const automationModel = await getAutomationModel();
    const automation = await automationModel.findOne({
      _id: new mongoose.Types.ObjectId(automationId),
      account: account._id
    });

    if (!automation) {
      return NextResponse.json({ error: 'Automation not found' }, { status: 404 });
    }

    // Use updateOne with validation disabled to avoid validation issues with other fields
    await automationModel.updateOne(
      { _id: new mongoose.Types.ObjectId(automationId), account: account._id },
      { $set: { isActive } },
      { runValidators: false }
    );

    return NextResponse.json({
      success: true,
      message: `Automation ${isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Toggle automation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
