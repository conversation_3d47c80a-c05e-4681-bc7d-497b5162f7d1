'use client';

document.addEventListener('DOMContentLoaded', function() {
  // Get the trigger and action select elements
  const triggerSelect = document.querySelector('select[name="triggerType"]');
  const actionSelect = document.querySelector('select[name="actionType"]');

  // Function to show/hide trigger conditions based on selected trigger
  function updateTriggerConditions() {
    if (!triggerSelect) return;

    const selectedTrigger = triggerSelect.value;

    // Handle project status change trigger
    const projectStatusFields = document.querySelectorAll('.trigger-conditions');
    projectStatusFields.forEach(field => {
      field.style.display = selectedTrigger === 'project_status_changed' ? 'block' : 'none';
    });
  }

  // Function to show/hide action configurations based on selected action
  function updateActionConfigs() {
    if (!actionSelect) return;

    const selectedAction = actionSelect.value;

    // Hide all action configuration sections
    document.querySelectorAll('.action-config').forEach(section => {
      section.style.display = 'none';
    });

    // Show the appropriate action configuration section
    if (selectedAction === 'send_email') {
      const emailConfig = document.querySelector('.action-config[data-action-type="send_email"]');
      if (emailConfig) emailConfig.style.display = 'block';
    } else if (selectedAction === 'send_sms') {
      const smsConfig = document.querySelector('.action-config[data-action-type="send_sms"]');
      if (smsConfig) smsConfig.style.display = 'block';
    }
  }

  // Add event listeners
  if (triggerSelect) {
    triggerSelect.addEventListener('change', updateTriggerConditions);
    // Initialize on page load
    setTimeout(updateTriggerConditions, 100);
  }

  if (actionSelect) {
    actionSelect.addEventListener('change', updateActionConfigs);
    // Initialize on page load
    setTimeout(updateActionConfigs, 100);
  }

  // Fix the active checkbox styling
  const activeCheckbox = document.querySelector('input[name="isActive"]');
  if (activeCheckbox) {
    const checkboxContainer = activeCheckbox.closest('.flex');
    if (checkboxContainer) {
      checkboxContainer.classList.add('justify-start');
      checkboxContainer.classList.remove('justify-center');
    }
  }
});
