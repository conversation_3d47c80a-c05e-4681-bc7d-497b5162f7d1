import { cache } from 'react';
import { notFound } from 'next/navigation';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';

  if (!mongoose.Types.ObjectId.isValid(id)) {
    notFound();
  }

  const automationModel = await getAutomationModel();
  const automation = await automationModel.findOne({ _id: new mongoose.Types.ObjectId(id) });

  if (!automation) {
    notFound();
  }

  // Populate related documents
  await automation.populate('documents.account');
  await automation.populate('documents.createdBy');
  await automation.populate('documents.modifiedBy');

  const permissions = await validatePermissions(automation);

  return { ...permissions };
});
