import Link from 'next/link';
import { Metada<PERSON> } from 'next';
import { Zap, Plus, ToggleLeft, ToggleRight, HelpCircle } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Widget } from '@/lib/widget';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AutomationToggle } from '@/components/ui/automation-toggle';

import { getData } from './helpers';

export const metadata: Metadata = {
  title: 'Automations | Trussi.ai',
};

export default async function AutomationPage() {
  const { automations, canCreate } = await getData();

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Automation Manager</h1>
        {canCreate && (
          <Button asChild>
            <Link href="/automation/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Automation
            </Link>
          </Button>
        )}
      </div>

      <Widget>
        <div className="p-6">
          <div className="flex items-center mb-6">
            <Zap className="h-6 w-6 mr-2 text-blue-500" />
            <h2 className="text-xl font-semibold">Your Automations</h2>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="ml-2 h-6 w-6">
                    <HelpCircle className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm p-4">
                  <div className="space-y-2">
                    <p className="font-medium">About Automations</p>
                    <p className="text-sm">Automations help you save time by automatically performing actions when specific events occur in your account. For example, you can:</p>
                    <ul className="text-sm list-disc pl-4 space-y-1">
                      <li>Send an email when a project status changes</li>
                      <li>Create a task when a new customer is added</li>
                      <li>Send notifications when quotes are created</li>
                      <li>Update project information automatically</li>
                    </ul>
                    <p className="text-sm mt-2">Each automation consists of a trigger (when something happens) and one or more actions (what should happen).</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {automations.length === 0 ? (
            <div className="text-center py-12 border rounded-md bg-muted/20">
              <Zap className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No automations found</h3>
              <p className="text-muted-foreground mb-6">
                Create your first automation to save time and streamline your workflow.
              </p>
              {canCreate && (
                <Button asChild>
                  <Link href="/automation/create">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Automation
                  </Link>
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Trigger</TableHead>
                  <TableHead>Actions</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Options</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {automations.map((automation) => (
                  <TableRow key={automation._id.toString()}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Link href={`/automation/${automation._id}`} className="font-medium hover:underline">
                          {automation.name}
                        </Link>

                      </div>
                    </TableCell>
                    <TableCell>
                      {automation.trigger.type.replace(/_/g, ' ')}
                    </TableCell>
                    <TableCell>{automation.actions.length} action(s)</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <AutomationToggle
                          name={`toggle-${automation._id}`}
                          defaultChecked={automation.isActive}
                          disabled={true}
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/automation/${automation._id}`}>Edit</Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </Widget>


    </>
  );
}
