import { NextRequest, NextResponse } from 'next/server';
import { getUserModel } from '@/schemas';
import { validateRequest } from '@/server/auth';

export async function GET(request: NextRequest) {
  try {
    // Validate the request
    const { user, account } = await validateRequest();
    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const type = searchParams.get('type') || 'email';

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    // Get user model
    const userModel = await getUserModel();

    // Build search query
    const searchQuery: any = { account: account._id };
    
    if (type === 'email') {
      searchQuery.$or = [
        { email: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
      ];
    } else {
      searchQuery.$or = [
        { phone: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
      ];
    }

    // Find users
    const users = await userModel
      .find(searchQuery)
      .select('name email phone')
      .limit(10)
      .lean();

    // Format results
    const suggestions = users.map(user => ({
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      phone: user.phone || '',
    }));

    return NextResponse.json(suggestions);
  } catch (error) {
    console.error('Error fetching user suggestions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
